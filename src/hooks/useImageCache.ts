/**
 * 图片缓存管理 Hook
 * 提供本地缓存机制，先从本地获取数据显示，然后在后台更新
 */

import { useState, useEffect, useCallback } from 'react';
import { chromeStorageGet, chromeStorageSet } from '@/utils/storage';
import type { BackgroundImage } from '@/types/background';
import type { RandomImageCategoryId, RandomImageThemeId } from '@/types/randomImage';

interface CachedImageData {
  images: BackgroundImage[];
  timestamp: number;
  category: RandomImageCategoryId;
  theme: RandomImageThemeId;
}

interface ImageCacheState {
  cachedImages: BackgroundImage[];
  isLoading: boolean;
  isUpdating: boolean;
  lastUpdated: number | null;
  error: string | null;
}

const CACHE_KEY_PREFIX = 'image_cache_';
const CACHE_EXPIRY_TIME = 30 * 60 * 1000; // 30分钟
const MAX_CACHE_ENTRIES = 20; // 最多缓存20个不同的分类/主题组合

/**
 * 生成缓存键
 */
function generateCacheKey(category: RandomImageCategoryId, theme: RandomImageThemeId): string {
  return `${CACHE_KEY_PREFIX}${category}_${theme}`;
}

/**
 * 图片缓存 Hook
 */
export function useImageCache() {
  const [state, setState] = useState<ImageCacheState>({
    cachedImages: [],
    isLoading: false,
    isUpdating: false,
    lastUpdated: null,
    error: null
  });

  /**
   * 从本地缓存获取图片
   */
  const getCachedImages = useCallback(async (
    category: RandomImageCategoryId,
    theme: RandomImageThemeId
  ): Promise<BackgroundImage[]> => {
    try {
      const cacheKey = generateCacheKey(category, theme);
      const result = await chromeStorageGet<CachedImageData>(cacheKey);
      
      if (result.success && result.data?.[cacheKey]) {
        const cachedData = result.data[cacheKey];
        const now = Date.now();
        
        // 检查缓存是否过期
        if (now - cachedData.timestamp < CACHE_EXPIRY_TIME) {
          return cachedData.images;
        }
      }
      
      return [];
    } catch (error) {
      console.warn('Failed to get cached images:', error);
      return [];
    }
  }, []);

  /**
   * 保存图片到本地缓存
   */
  const setCachedImages = useCallback(async (
    category: RandomImageCategoryId,
    theme: RandomImageThemeId,
    images: BackgroundImage[]
  ): Promise<void> => {
    try {
      const cacheKey = generateCacheKey(category, theme);
      const cachedData: CachedImageData = {
        images,
        timestamp: Date.now(),
        category,
        theme
      };
      
      await chromeStorageSet({ [cacheKey]: cachedData });
      
      // 清理旧的缓存条目
      await cleanupOldCache();
    } catch (error) {
      console.warn('Failed to cache images:', error);
    }
  }, []);

  /**
   * 清理旧的缓存条目
   */
  const cleanupOldCache = useCallback(async (): Promise<void> => {
    try {
      // 获取所有存储的数据
      const result = await chromeStorageGet<Record<string, any>>('');
      if (!result.success || !result.data) return;

      // 找到所有图片缓存键
      const cacheKeys = Object.keys(result.data).filter(key => 
        key.startsWith(CACHE_KEY_PREFIX)
      );

      if (cacheKeys.length <= MAX_CACHE_ENTRIES) return;

      // 按时间戳排序，删除最旧的条目
      const cacheEntries = cacheKeys
        .map(key => ({
          key,
          timestamp: result.data[key]?.timestamp || 0
        }))
        .sort((a, b) => a.timestamp - b.timestamp);

      const keysToDelete = cacheEntries
        .slice(0, cacheKeys.length - MAX_CACHE_ENTRIES)
        .map(entry => entry.key);

      // 删除旧的缓存条目
      if (keysToDelete.length > 0) {
        const deleteData: Record<string, undefined> = {};
        keysToDelete.forEach(key => {
          deleteData[key] = undefined;
        });
        await chromeStorageSet(deleteData);
      }
    } catch (error) {
      console.warn('Failed to cleanup old cache:', error);
    }
  }, []);

  /**
   * 加载图片（先从缓存，后台更新）
   */
  const loadImages = useCallback(async (
    category: RandomImageCategoryId,
    theme: RandomImageThemeId,
    fetcher: () => Promise<BackgroundImage[]>
  ): Promise<BackgroundImage[]> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // 1. 先从缓存获取数据
      const cachedImages = await getCachedImages(category, theme);
      
      if (cachedImages.length > 0) {
        // 有缓存数据，立即显示
        setState(prev => ({
          ...prev,
          cachedImages,
          isLoading: false,
          lastUpdated: Date.now()
        }));

        // 在后台更新数据
        setState(prev => ({ ...prev, isUpdating: true }));
        
        try {
          const freshImages = await fetcher();
          if (freshImages.length > 0) {
            await setCachedImages(category, theme, freshImages);
            setState(prev => ({
              ...prev,
              cachedImages: freshImages,
              isUpdating: false,
              lastUpdated: Date.now()
            }));
          } else {
            setState(prev => ({ ...prev, isUpdating: false }));
          }
        } catch (updateError) {
          console.warn('Background update failed:', updateError);
          setState(prev => ({ ...prev, isUpdating: false }));
        }

        return cachedImages;
      } else {
        // 没有缓存数据，直接获取新数据
        const freshImages = await fetcher();
        
        if (freshImages.length > 0) {
          await setCachedImages(category, theme, freshImages);
          setState(prev => ({
            ...prev,
            cachedImages: freshImages,
            isLoading: false,
            lastUpdated: Date.now()
          }));
        } else {
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: '未能获取到图片数据'
          }));
        }

        return freshImages;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载图片失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return [];
    }
  }, [getCachedImages, setCachedImages]);

  /**
   * 强制刷新（忽略缓存）
   */
  const forceRefresh = useCallback(async (
    category: RandomImageCategoryId,
    theme: RandomImageThemeId,
    fetcher: () => Promise<BackgroundImage[]>
  ): Promise<BackgroundImage[]> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const freshImages = await fetcher();
      
      if (freshImages.length > 0) {
        await setCachedImages(category, theme, freshImages);
        setState(prev => ({
          ...prev,
          cachedImages: freshImages,
          isLoading: false,
          lastUpdated: Date.now()
        }));
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: '未能获取到图片数据'
        }));
      }

      return freshImages;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新图片失败';
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMessage
      }));
      return [];
    }
  }, [setCachedImages]);

  /**
   * 清除所有缓存
   */
  const clearAllCache = useCallback(async (): Promise<void> => {
    try {
      const result = await chromeStorageGet<Record<string, any>>('');
      if (!result.success || !result.data) return;

      const cacheKeys = Object.keys(result.data).filter(key => 
        key.startsWith(CACHE_KEY_PREFIX)
      );

      if (cacheKeys.length > 0) {
        const deleteData: Record<string, undefined> = {};
        cacheKeys.forEach(key => {
          deleteData[key] = undefined;
        });
        await chromeStorageSet(deleteData);
      }

      setState(prev => ({
        ...prev,
        cachedImages: [],
        lastUpdated: null
      }));
    } catch (error) {
      console.warn('Failed to clear cache:', error);
    }
  }, []);

  return {
    ...state,
    loadImages,
    forceRefresh,
    clearAllCache
  };
}
