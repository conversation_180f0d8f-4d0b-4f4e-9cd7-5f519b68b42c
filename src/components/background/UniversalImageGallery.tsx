/**
 * 通用背景图片画廊组件
 * 使用统一的BackgroundImageManager服务
 */

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  RefreshCw,
  Info,
  Grid,
  List,
  Loader2
} from 'lucide-react';

import { useSettings } from '@/hooks/useSettings';
import { useImageCache } from '@/hooks/useImageCache';
import { backgroundImageManager } from '@/services/background';
import type {
  BackgroundImage,
  BackgroundImageSource,
} from '@/types/background';
import { RANDOM_IMAGE_CATEGORIES, RANDOM_IMAGE_THEMES } from '@/types/randomImage';

interface UniversalImageGalleryProps {
  onSelect?: (image: BackgroundImage, imageUrl: string) => void;
  onSelectMultiple?: (images: BackgroundImage[]) => void;
  className?: string;
  initialSource?: BackgroundImageSource;
  initialCategory?: string;
  initialTheme?: string;
  maxHistory?: number;
}


export function UniversalImageGallery({
  className = '',
  initialCategory = 'all',
  initialTheme = 'all'
}: UniversalImageGalleryProps) {
  const { settings, updateSettings, isLoading } = useSettings();
  const imageCache = useImageCache();

  // 使用 useMemo 来稳定初始值，避免因为 settings 变化导致的重新渲染
  const stableInitialCategory = useMemo(() => {
    return settings.background.randomImageCategory || initialCategory;
  }, [settings.background.randomImageCategory, initialCategory]);

  const stableInitialTheme = useMemo(() => {
    return settings.background.randomImageTheme || initialTheme;
  }, [settings.background.randomImageTheme, initialTheme]);

  const [selectedCategory, setSelectedCategory] = useState(stableInitialCategory);
  const [selectedTheme, setSelectedTheme] = useState(stableInitialTheme);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // 当设置加载完成后，同步状态
  useEffect(() => {
    if (!isLoading) {
      setSelectedCategory(settings.background.randomImageCategory || initialCategory);
      setSelectedTheme(settings.background.randomImageTheme || initialTheme);
    }
  }, [isLoading, settings.background.randomImageCategory, settings.background.randomImageTheme, initialCategory, initialTheme]);

  // 当分类或主题改变时，加载缓存的图片
  useEffect(() => {
    if (!isLoading && selectedCategory && selectedTheme) {
      loadCachedImages();
    }
  }, [selectedCategory, selectedTheme, isLoading]);

  /**
   * 加载缓存的图片（先显示缓存，后台更新）
   */
  const loadCachedImages = useCallback(async () => {
    await imageCache.loadImages(
      selectedCategory,
      selectedTheme,
      async () => {
        // 获取新的图片数据
        const filters = {
          category: selectedCategory !== 'all' ? selectedCategory : undefined,
          theme: selectedTheme !== 'all' ? selectedTheme : undefined
        };

        return await backgroundImageManager.getRandomImagesFromSource('random', 8, filters);
      }
    );
  }, [selectedCategory, selectedTheme, imageCache]);

  /**
   * 强制刷新图片
   */
  const handleRefresh = useCallback(async () => {
    await imageCache.forceRefresh(
      selectedCategory,
      selectedTheme,
      async () => {
        const filters = {
          category: selectedCategory !== 'all' ? selectedCategory : undefined,
          theme: selectedTheme !== 'all' ? selectedTheme : undefined
        };

        return await backgroundImageManager.getRandomImagesFromSource('random', 8, filters);
      }
    );
  }, [selectedCategory, selectedTheme, imageCache]);



  return (
    <div className={`space-y-4 ${className}`}>
      {/* 控制面板 */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              {imageCache.isLoading ? (
                <Loader2 className="w-4 h-4 text-indigo-600 animate-spin" />
              ) : imageCache.isUpdating ? (
                <RefreshCw className="w-4 h-4 text-indigo-600 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4 text-indigo-600" />
              )}
              背景图片
              {imageCache.isUpdating && (
                <span className="text-xs text-gray-500">(后台更新中...)</span>
              )}
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={imageCache.isLoading}
                className="h-7 px-2"
                title="刷新图片"
              >
                <RefreshCw className={`w-3 h-3 ${imageCache.isLoading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="h-7 px-2"
              >
                {viewMode === 'grid' ? <List className="w-3 h-3" /> : <Grid className="w-3 h-3" />}
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-3">
          {/* 筛选选项 */}
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-1">
              <label className="text-xs text-gray-600">分类</label>
              <Select
                value={selectedCategory}
                onValueChange={(value) => {
                  setSelectedCategory(value);
                  // 使用防抖更新设置，避免频繁的状态更新
                  updateSettings('background', {
                    ...settings.background,
                    randomImageCategory: value
                  });
                }}
                disabled={isLoading}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue placeholder="选择分类..." />
                </SelectTrigger>
                <SelectContent>
                  {RANDOM_IMAGE_CATEGORIES.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-1">
              <label className="text-xs text-gray-600">主题</label>
              <Select
                value={selectedTheme}
                onValueChange={(value) => {
                  setSelectedTheme(value);
                  // 使用防抖更新设置，避免频繁的状态更新
                  updateSettings('background', {
                    ...settings.background,
                    randomImageTheme: value
                  });
                }}
                disabled={isLoading}
              >
                <SelectTrigger className="h-8 text-sm">
                  <SelectValue placeholder="选择主题..." />
                </SelectTrigger>
                <SelectContent>
                  {RANDOM_IMAGE_THEMES.map((theme) => (
                    <SelectItem key={theme.id} value={theme.id}>
                      {theme.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 图片展示区域 */}
      {imageCache.cachedImages.length > 0 && (
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">
              缓存的图片 ({imageCache.cachedImages.length})
              {imageCache.lastUpdated && (
                <span className="text-xs text-gray-500 font-normal ml-2">
                  最后更新: {new Date(imageCache.lastUpdated).toLocaleTimeString()}
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-2">
              {imageCache.cachedImages.slice(0, 4).map((image, index) => (
                <div
                  key={image.id}
                  className="relative aspect-video rounded-lg overflow-hidden bg-gray-100 cursor-pointer hover:ring-2 hover:ring-indigo-500 transition-all"
                  onClick={() => {
                    // 这里可以添加选择图片的逻辑
                    console.log('Selected image:', image);
                  }}
                >
                  <img
                    src={image.url}
                    alt={image.description || `背景图片 ${index + 1}`}
                    className="w-full h-full object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/20 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                    <span className="text-white text-xs font-medium">点击选择</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 错误提示 */}
      {imageCache.error && (
        <Card className="bg-red-50 border-red-200">
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <Info className="w-4 h-4 text-red-600 mt-0.5 flex-shrink-0" />
              <div className="text-xs text-red-800">
                <p className="font-medium mb-1">加载失败：</p>
                <p className="text-red-700">{imageCache.error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 使用提示 */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-3">
          <div className="flex items-start gap-2">
            <Info className="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-xs text-blue-800">
              <p className="font-medium mb-1">使用说明：</p>
              <p className="text-blue-700">
                选择喜欢的图片分类，图片会自动从缓存加载。点击刷新按钮获取新的随机背景图片。
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
